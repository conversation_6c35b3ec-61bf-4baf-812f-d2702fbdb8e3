.class public interface abstract annotation Lorg/checkerframework/common/initializedfields/qual/PolyInitializedFields;
.super Ljava/lang/Object;
.source "PolyInitializedFields.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE_USE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->TYPE_PARAMETER:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lorg/checkerframework/framework/qual/PolymorphicQualifier;
    value = Lorg/checkerframework/common/initializedfields/qual/InitializedFields;
.end annotation
