.class public interface abstract annotation Lorg/checkerframework/framework/qual/DefaultFor;
.super Ljava/lang/Object;
.source "DefaultFor.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lorg/checkerframework/framework/qual/DefaultFor;
        names = {}
        namesExceptions = {}
        typeKinds = {}
        types = {}
        value = {}
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->ANNOTATION_TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract names()[Ljava/lang/String;
.end method

.method public abstract namesExceptions()[Ljava/lang/String;
.end method

.method public abstract typeKinds()[Lorg/checkerframework/framework/qual/TypeKind;
.end method

.method public abstract types()[Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()[",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end method

.method public abstract value()[Lorg/checkerframework/framework/qual/TypeUseLocation;
.end method
