.class public interface abstract annotation Lorg/checkerframework/framework/qual/QualifierForLiterals;
.super Ljava/lang/Object;
.source "QualifierForLiterals.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lorg/checkerframework/framework/qual/QualifierForLiterals;
        stringPatterns = {}
        value = {}
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->ANNOTATION_TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract stringPatterns()[Ljava/lang/String;
.end method

.method public abstract value()[Lorg/checkerframework/framework/qual/LiteralKind;
.end method
