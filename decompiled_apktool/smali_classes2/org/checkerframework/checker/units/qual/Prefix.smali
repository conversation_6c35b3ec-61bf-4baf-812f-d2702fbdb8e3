.class public final enum Lorg/checkerframework/checker/units/qual/Prefix;
.super Ljava/lang/Enum;
.source "Prefix.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/checkerframework/checker/units/qual/Prefix;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum atto:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum centi:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum deca:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum deci:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum exa:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum femto:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum giga:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum hecto:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum kilo:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum mega:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum micro:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum milli:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum nano:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum one:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum peta:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum pico:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum tera:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum yocto:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum yotta:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum zepto:Lorg/checkerframework/checker/units/qual/Prefix;

.field public static final enum zetta:Lorg/checkerframework/checker/units/qual/Prefix;


# direct methods
.method private static synthetic $values()[Lorg/checkerframework/checker/units/qual/Prefix;
    .locals 3

    const/16 v0, 0x15

    .line 35
    new-array v0, v0, [Lorg/checkerframework/checker/units/qual/Prefix;

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->yotta:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->zetta:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->exa:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->peta:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->tera:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->giga:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->mega:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->kilo:Lorg/checkerframework/checker/units/qual/Prefix;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->hecto:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->deca:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->one:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->deci:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->centi:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->milli:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->micro:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0xe

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->nano:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0xf

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->pico:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0x10

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->femto:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0x11

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->atto:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0x12

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->zepto:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0x13

    aput-object v1, v0, v2

    sget-object v1, Lorg/checkerframework/checker/units/qual/Prefix;->yocto:Lorg/checkerframework/checker/units/qual/Prefix;

    const/16 v2, 0x14

    aput-object v1, v0, v2

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    .line 37
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "yotta"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->yotta:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 39
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "zetta"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->zetta:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 41
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "exa"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->exa:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 43
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "peta"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->peta:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 45
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "tera"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->tera:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 47
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "giga"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->giga:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 49
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "mega"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->mega:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 51
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "kilo"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->kilo:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 53
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "hecto"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->hecto:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 55
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "deca"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->deca:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 57
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "one"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->one:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 59
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "deci"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->deci:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 61
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "centi"

    const/16 v2, 0xc

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->centi:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 63
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "milli"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->milli:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 65
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "micro"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->micro:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 67
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "nano"

    const/16 v2, 0xf

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->nano:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 69
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "pico"

    const/16 v2, 0x10

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->pico:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 71
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "femto"

    const/16 v2, 0x11

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->femto:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 73
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "atto"

    const/16 v2, 0x12

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->atto:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 75
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "zepto"

    const/16 v2, 0x13

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->zepto:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 77
    new-instance v0, Lorg/checkerframework/checker/units/qual/Prefix;

    const-string v1, "yocto"

    const/16 v2, 0x14

    invoke-direct {v0, v1, v2}, Lorg/checkerframework/checker/units/qual/Prefix;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->yocto:Lorg/checkerframework/checker/units/qual/Prefix;

    .line 35
    invoke-static {}, Lorg/checkerframework/checker/units/qual/Prefix;->$values()[Lorg/checkerframework/checker/units/qual/Prefix;

    move-result-object v0

    sput-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->$VALUES:[Lorg/checkerframework/checker/units/qual/Prefix;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 35
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/checkerframework/checker/units/qual/Prefix;
    .locals 1

    .line 35
    const-class v0, Lorg/checkerframework/checker/units/qual/Prefix;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lorg/checkerframework/checker/units/qual/Prefix;

    return-object p0
.end method

.method public static values()[Lorg/checkerframework/checker/units/qual/Prefix;
    .locals 1

    .line 35
    sget-object v0, Lorg/checkerframework/checker/units/qual/Prefix;->$VALUES:[Lorg/checkerframework/checker/units/qual/Prefix;

    invoke-virtual {v0}, [Lorg/checkerframework/checker/units/qual/Prefix;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/checkerframework/checker/units/qual/Prefix;

    return-object v0
.end method
