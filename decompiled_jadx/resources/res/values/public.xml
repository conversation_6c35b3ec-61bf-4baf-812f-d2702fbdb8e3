<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="btn_checkbox_to_checked_box_inner_merged_animation" id="0x7f01000c" />
    <public type="anim" name="btn_checkbox_to_checked_box_outer_merged_animation" id="0x7f01000d" />
    <public type="anim" name="btn_checkbox_to_checked_icon_null_animation" id="0x7f01000e" />
    <public type="anim" name="btn_checkbox_to_unchecked_box_inner_merged_animation" id="0x7f01000f" />
    <public type="anim" name="btn_checkbox_to_unchecked_check_path_merged_animation" id="0x7f010010" />
    <public type="anim" name="btn_checkbox_to_unchecked_icon_null_animation" id="0x7f010011" />
    <public type="anim" name="btn_radio_to_off_mtrl_dot_group_animation" id="0x7f010012" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_animation" id="0x7f010013" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_path_animation" id="0x7f010014" />
    <public type="anim" name="btn_radio_to_on_mtrl_dot_group_animation" id="0x7f010015" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_animation" id="0x7f010016" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_path_animation" id="0x7f010017" />
    <public type="anim" name="fragment_fast_out_extra_slow_in" id="0x7f010018" />
    <public type="animator" name="fragment_close_enter" id="0x7f020000" />
    <public type="animator" name="fragment_close_exit" id="0x7f020001" />
    <public type="animator" name="fragment_fade_enter" id="0x7f020002" />
    <public type="animator" name="fragment_fade_exit" id="0x7f020003" />
    <public type="animator" name="fragment_open_enter" id="0x7f020004" />
    <public type="animator" name="fragment_open_exit" id="0x7f020005" />
    <public type="attr" name="actionBarDivider" id="0x7f030000" />
    <public type="attr" name="actionBarItemBackground" id="0x7f030001" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f030002" />
    <public type="attr" name="actionBarSize" id="0x7f030003" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f030004" />
    <public type="attr" name="actionBarStyle" id="0x7f030005" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f030006" />
    <public type="attr" name="actionBarTabStyle" id="0x7f030007" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f030008" />
    <public type="attr" name="actionBarTheme" id="0x7f030009" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f03000a" />
    <public type="attr" name="actionButtonStyle" id="0x7f03000b" />
    <public type="attr" name="actionDropDownStyle" id="0x7f03000c" />
    <public type="attr" name="actionLayout" id="0x7f03000d" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f03000e" />
    <public type="attr" name="actionMenuTextColor" id="0x7f03000f" />
    <public type="attr" name="actionModeBackground" id="0x7f030010" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f030011" />
    <public type="attr" name="actionModeCloseContentDescription" id="0x7f030012" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f030013" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f030014" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f030015" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f030016" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f030017" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f030018" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f030019" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f03001a" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f03001b" />
    <public type="attr" name="actionModeStyle" id="0x7f03001c" />
    <public type="attr" name="actionModeTheme" id="0x7f03001d" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f03001e" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f03001f" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f030020" />
    <public type="attr" name="actionProviderClass" id="0x7f030021" />
    <public type="attr" name="actionViewClass" id="0x7f030022" />
    <public type="attr" name="activityAction" id="0x7f030023" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f030024" />
    <public type="attr" name="activityName" id="0x7f030025" />
    <public type="attr" name="adjustable" id="0x7f030026" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f030027" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f030028" />
    <public type="attr" name="alertDialogStyle" id="0x7f030029" />
    <public type="attr" name="alertDialogTheme" id="0x7f03002a" />
    <public type="attr" name="allowDividerAbove" id="0x7f03002b" />
    <public type="attr" name="allowDividerAfterLastItem" id="0x7f03002c" />
    <public type="attr" name="allowDividerBelow" id="0x7f03002d" />
    <public type="attr" name="allowStacking" id="0x7f03002e" />
    <public type="attr" name="alpha" id="0x7f03002f" />
    <public type="attr" name="alphabeticModifiers" id="0x7f030030" />
    <public type="attr" name="alwaysExpand" id="0x7f030031" />
    <public type="attr" name="animationBackgroundColor" id="0x7f030032" />
    <public type="attr" name="arrowHeadLength" id="0x7f030033" />
    <public type="attr" name="arrowShaftLength" id="0x7f030034" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f030035" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f030036" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f030037" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f030038" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f030039" />
    <public type="attr" name="autoSizeTextType" id="0x7f03003a" />
    <public type="attr" name="background" id="0x7f03003b" />
    <public type="attr" name="backgroundSplit" id="0x7f03003c" />
    <public type="attr" name="backgroundStacked" id="0x7f03003d" />
    <public type="attr" name="backgroundTint" id="0x7f03003e" />
    <public type="attr" name="backgroundTintMode" id="0x7f03003f" />
    <public type="attr" name="barLength" id="0x7f030040" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f030041" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f030042" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f030043" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f030044" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f030045" />
    <public type="attr" name="buttonBarStyle" id="0x7f030046" />
    <public type="attr" name="buttonCompat" id="0x7f030047" />
    <public type="attr" name="buttonGravity" id="0x7f030048" />
    <public type="attr" name="buttonIconDimen" id="0x7f030049" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f03004a" />
    <public type="attr" name="buttonSize" id="0x7f03004b" />
    <public type="attr" name="buttonStyle" id="0x7f03004c" />
    <public type="attr" name="buttonStyleSmall" id="0x7f03004d" />
    <public type="attr" name="buttonTint" id="0x7f03004e" />
    <public type="attr" name="buttonTintMode" id="0x7f03004f" />
    <public type="attr" name="checkBoxPreferenceStyle" id="0x7f030050" />
    <public type="attr" name="checkMarkCompat" id="0x7f030051" />
    <public type="attr" name="checkMarkTint" id="0x7f030052" />
    <public type="attr" name="checkMarkTintMode" id="0x7f030053" />
    <public type="attr" name="checkboxStyle" id="0x7f030054" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f030055" />
    <public type="attr" name="circleCrop" id="0x7f030056" />
    <public type="attr" name="clearTop" id="0x7f030057" />
    <public type="attr" name="closeIcon" id="0x7f030058" />
    <public type="attr" name="closeItemLayout" id="0x7f030059" />
    <public type="attr" name="collapseContentDescription" id="0x7f03005a" />
    <public type="attr" name="collapseIcon" id="0x7f03005b" />
    <public type="attr" name="color" id="0x7f03005c" />
    <public type="attr" name="colorAccent" id="0x7f03005d" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f03005e" />
    <public type="attr" name="colorButtonNormal" id="0x7f03005f" />
    <public type="attr" name="colorControlActivated" id="0x7f030060" />
    <public type="attr" name="colorControlHighlight" id="0x7f030061" />
    <public type="attr" name="colorControlNormal" id="0x7f030062" />
    <public type="attr" name="colorError" id="0x7f030063" />
    <public type="attr" name="colorPrimary" id="0x7f030064" />
    <public type="attr" name="colorPrimaryDark" id="0x7f030065" />
    <public type="attr" name="colorScheme" id="0x7f030066" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f030067" />
    <public type="attr" name="commitIcon" id="0x7f030068" />
    <public type="attr" name="contentDescription" id="0x7f030069" />
    <public type="attr" name="contentInsetEnd" id="0x7f03006a" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f03006b" />
    <public type="attr" name="contentInsetLeft" id="0x7f03006c" />
    <public type="attr" name="contentInsetRight" id="0x7f03006d" />
    <public type="attr" name="contentInsetStart" id="0x7f03006e" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f03006f" />
    <public type="attr" name="controlBackground" id="0x7f030070" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f030071" />
    <public type="attr" name="customNavigationLayout" id="0x7f030072" />
    <public type="attr" name="defaultQueryHint" id="0x7f030073" />
    <public type="attr" name="defaultValue" id="0x7f030074" />
    <public type="attr" name="dependency" id="0x7f030075" />
    <public type="attr" name="dialogCornerRadius" id="0x7f030076" />
    <public type="attr" name="dialogIcon" id="0x7f030077" />
    <public type="attr" name="dialogLayout" id="0x7f030078" />
    <public type="attr" name="dialogMessage" id="0x7f030079" />
    <public type="attr" name="dialogPreferenceStyle" id="0x7f03007a" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f03007b" />
    <public type="attr" name="dialogTheme" id="0x7f03007c" />
    <public type="attr" name="dialogTitle" id="0x7f03007d" />
    <public type="attr" name="disableDependentsState" id="0x7f03007e" />
    <public type="attr" name="displayOptions" id="0x7f03007f" />
    <public type="attr" name="divider" id="0x7f030080" />
    <public type="attr" name="dividerHorizontal" id="0x7f030081" />
    <public type="attr" name="dividerPadding" id="0x7f030082" />
    <public type="attr" name="dividerVertical" id="0x7f030083" />
    <public type="attr" name="drawableBottomCompat" id="0x7f030084" />
    <public type="attr" name="drawableEndCompat" id="0x7f030085" />
    <public type="attr" name="drawableLeftCompat" id="0x7f030086" />
    <public type="attr" name="drawableRightCompat" id="0x7f030087" />
    <public type="attr" name="drawableSize" id="0x7f030088" />
    <public type="attr" name="drawableStartCompat" id="0x7f030089" />
    <public type="attr" name="drawableTint" id="0x7f03008a" />
    <public type="attr" name="drawableTintMode" id="0x7f03008b" />
    <public type="attr" name="drawableTopCompat" id="0x7f03008c" />
    <public type="attr" name="drawerArrowStyle" id="0x7f03008d" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f03008e" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f03008f" />
    <public type="attr" name="dropdownPreferenceStyle" id="0x7f030090" />
    <public type="attr" name="editTextBackground" id="0x7f030091" />
    <public type="attr" name="editTextColor" id="0x7f030092" />
    <public type="attr" name="editTextPreferenceStyle" id="0x7f030093" />
    <public type="attr" name="editTextStyle" id="0x7f030094" />
    <public type="attr" name="elevation" id="0x7f030095" />
    <public type="attr" name="emojiCompatEnabled" id="0x7f030096" />
    <public type="attr" name="enableCopying" id="0x7f030097" />
    <public type="attr" name="enabled" id="0x7f030098" />
    <public type="attr" name="entries" id="0x7f030099" />
    <public type="attr" name="entryValues" id="0x7f03009a" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f03009b" />
    <public type="attr" name="fastScrollEnabled" id="0x7f03009c" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f03009d" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f03009e" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f03009f" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f0300a0" />
    <public type="attr" name="finishPrimaryWithPlaceholder" id="0x7f0300a1" />
    <public type="attr" name="finishPrimaryWithSecondary" id="0x7f0300a2" />
    <public type="attr" name="finishSecondaryWithPrimary" id="0x7f0300a3" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f0300a4" />
    <public type="attr" name="font" id="0x7f0300a5" />
    <public type="attr" name="fontFamily" id="0x7f0300a6" />
    <public type="attr" name="fontProviderAuthority" id="0x7f0300a7" />
    <public type="attr" name="fontProviderCerts" id="0x7f0300a8" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f0300a9" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f0300aa" />
    <public type="attr" name="fontProviderPackage" id="0x7f0300ab" />
    <public type="attr" name="fontProviderQuery" id="0x7f0300ac" />
    <public type="attr" name="fontProviderSystemFontFamily" id="0x7f0300ad" />
    <public type="attr" name="fontStyle" id="0x7f0300ae" />
    <public type="attr" name="fontVariationSettings" id="0x7f0300af" />
    <public type="attr" name="fontWeight" id="0x7f0300b0" />
    <public type="attr" name="fragment" id="0x7f0300b1" />
    <public type="attr" name="gapBetweenBars" id="0x7f0300b2" />
    <public type="attr" name="goIcon" id="0x7f0300b3" />
    <public type="attr" name="height" id="0x7f0300b4" />
    <public type="attr" name="hideOnContentScroll" id="0x7f0300b5" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f0300b6" />
    <public type="attr" name="homeLayout" id="0x7f0300b7" />
    <public type="attr" name="icon" id="0x7f0300b8" />
    <public type="attr" name="iconSpaceReserved" id="0x7f0300b9" />
    <public type="attr" name="iconTint" id="0x7f0300ba" />
    <public type="attr" name="iconTintMode" id="0x7f0300bb" />
    <public type="attr" name="iconifiedByDefault" id="0x7f0300bc" />
    <public type="attr" name="imageAspectRatio" id="0x7f0300bd" />
    <public type="attr" name="imageAspectRatioAdjust" id="0x7f0300be" />
    <public type="attr" name="imageButtonStyle" id="0x7f0300bf" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f0300c0" />
    <public type="attr" name="initialActivityCount" id="0x7f0300c1" />
    <public type="attr" name="initialExpandedChildrenCount" id="0x7f0300c2" />
    <public type="attr" name="isLightTheme" id="0x7f0300c3" />
    <public type="attr" name="isPreferenceVisible" id="0x7f0300c4" />
    <public type="attr" name="itemPadding" id="0x7f0300c5" />
    <public type="attr" name="key" id="0x7f0300c6" />
    <public type="attr" name="keylines" id="0x7f0300c7" />
    <public type="attr" name="lStar" id="0x7f0300c8" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f0300c9" />
    <public type="attr" name="layout" id="0x7f0300ca" />
    <public type="attr" name="layoutManager" id="0x7f0300cb" />
    <public type="attr" name="layout_anchor" id="0x7f0300cc" />
    <public type="attr" name="layout_anchorGravity" id="0x7f0300cd" />
    <public type="attr" name="layout_behavior" id="0x7f0300ce" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f0300cf" />
    <public type="attr" name="layout_insetEdge" id="0x7f0300d0" />
    <public type="attr" name="layout_keyline" id="0x7f0300d1" />
    <public type="attr" name="lineHeight" id="0x7f0300d2" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f0300d3" />
    <public type="attr" name="listChoiceIndicatorMultipleAnimated" id="0x7f0300d4" />
    <public type="attr" name="listChoiceIndicatorSingleAnimated" id="0x7f0300d5" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f0300d6" />
    <public type="attr" name="listItemLayout" id="0x7f0300d7" />
    <public type="attr" name="listLayout" id="0x7f0300d8" />
    <public type="attr" name="listMenuViewStyle" id="0x7f0300d9" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f0300da" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f0300db" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f0300dc" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f0300dd" />
    <public type="attr" name="listPreferredItemPaddingEnd" id="0x7f0300de" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f0300df" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f0300e0" />
    <public type="attr" name="listPreferredItemPaddingStart" id="0x7f0300e1" />
    <public type="attr" name="logo" id="0x7f0300e2" />
    <public type="attr" name="logoDescription" id="0x7f0300e3" />
    <public type="attr" name="maxButtonHeight" id="0x7f0300e4" />
    <public type="attr" name="maxHeight" id="0x7f0300e5" />
    <public type="attr" name="maxWidth" id="0x7f0300e6" />
    <public type="attr" name="measureWithLargestChild" id="0x7f0300e7" />
    <public type="attr" name="menu" id="0x7f0300e8" />
    <public type="attr" name="min" id="0x7f0300e9" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f0300ea" />
    <public type="attr" name="navigationContentDescription" id="0x7f0300eb" />
    <public type="attr" name="navigationIcon" id="0x7f0300ec" />
    <public type="attr" name="navigationMode" id="0x7f0300ed" />
    <public type="attr" name="negativeButtonText" id="0x7f0300ee" />
    <public type="attr" name="nestedScrollViewStyle" id="0x7f0300ef" />
    <public type="attr" name="numericModifiers" id="0x7f0300f0" />
    <public type="attr" name="order" id="0x7f0300f1" />
    <public type="attr" name="orderingFromXml" id="0x7f0300f2" />
    <public type="attr" name="overlapAnchor" id="0x7f0300f3" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f0300f4" />
    <public type="attr" name="paddingEnd" id="0x7f0300f5" />
    <public type="attr" name="paddingStart" id="0x7f0300f6" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f0300f7" />
    <public type="attr" name="panelBackground" id="0x7f0300f8" />
    <public type="attr" name="panelMenuListTheme" id="0x7f0300f9" />
    <public type="attr" name="panelMenuListWidth" id="0x7f0300fa" />
    <public type="attr" name="persistent" id="0x7f0300fb" />
    <public type="attr" name="placeholderActivityName" id="0x7f0300fc" />
    <public type="attr" name="popupMenuStyle" id="0x7f0300fd" />
    <public type="attr" name="popupTheme" id="0x7f0300fe" />
    <public type="attr" name="popupWindowStyle" id="0x7f0300ff" />
    <public type="attr" name="positiveButtonText" id="0x7f030100" />
    <public type="attr" name="preferenceCategoryStyle" id="0x7f030101" />
    <public type="attr" name="preferenceCategoryTitleTextAppearance" id="0x7f030102" />
    <public type="attr" name="preferenceCategoryTitleTextColor" id="0x7f030103" />
    <public type="attr" name="preferenceFragmentCompatStyle" id="0x7f030104" />
    <public type="attr" name="preferenceFragmentListStyle" id="0x7f030105" />
    <public type="attr" name="preferenceFragmentStyle" id="0x7f030106" />
    <public type="attr" name="preferenceInformationStyle" id="0x7f030107" />
    <public type="attr" name="preferenceScreenStyle" id="0x7f030108" />
    <public type="attr" name="preferenceStyle" id="0x7f030109" />
    <public type="attr" name="preferenceTheme" id="0x7f03010a" />
    <public type="attr" name="preserveIconSpacing" id="0x7f03010b" />
    <public type="attr" name="primaryActivityName" id="0x7f03010c" />
    <public type="attr" name="progressBarPadding" id="0x7f03010d" />
    <public type="attr" name="progressBarStyle" id="0x7f03010e" />
    <public type="attr" name="queryBackground" id="0x7f03010f" />
    <public type="attr" name="queryHint" id="0x7f030110" />
    <public type="attr" name="queryPatterns" id="0x7f030111" />
    <public type="attr" name="radioButtonStyle" id="0x7f030112" />
    <public type="attr" name="ratingBarStyle" id="0x7f030113" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f030114" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f030115" />
    <public type="attr" name="reverseLayout" id="0x7f030116" />
    <public type="attr" name="scopeUris" id="0x7f030117" />
    <public type="attr" name="searchHintIcon" id="0x7f030118" />
    <public type="attr" name="searchIcon" id="0x7f030119" />
    <public type="attr" name="searchViewStyle" id="0x7f03011a" />
    <public type="attr" name="secondaryActivityAction" id="0x7f03011b" />
    <public type="attr" name="secondaryActivityName" id="0x7f03011c" />
    <public type="attr" name="seekBarIncrement" id="0x7f03011d" />
    <public type="attr" name="seekBarPreferenceStyle" id="0x7f03011e" />
    <public type="attr" name="seekBarStyle" id="0x7f03011f" />
    <public type="attr" name="selectable" id="0x7f030120" />
    <public type="attr" name="selectableItemBackground" id="0x7f030121" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f030122" />
    <public type="attr" name="shortcutMatchRequired" id="0x7f030123" />
    <public type="attr" name="shouldDisableView" id="0x7f030124" />
    <public type="attr" name="showAsAction" id="0x7f030125" />
    <public type="attr" name="showDividers" id="0x7f030126" />
    <public type="attr" name="showSeekBarValue" id="0x7f030127" />
    <public type="attr" name="showText" id="0x7f030128" />
    <public type="attr" name="showTitle" id="0x7f030129" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f03012a" />
    <public type="attr" name="singleLineTitle" id="0x7f03012b" />
    <public type="attr" name="spanCount" id="0x7f03012c" />
    <public type="attr" name="spinBars" id="0x7f03012d" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f03012e" />
    <public type="attr" name="spinnerStyle" id="0x7f03012f" />
    <public type="attr" name="splitLayoutDirection" id="0x7f030130" />
    <public type="attr" name="splitMaxAspectRatioInLandscape" id="0x7f030131" />
    <public type="attr" name="splitMaxAspectRatioInPortrait" id="0x7f030132" />
    <public type="attr" name="splitMinHeightDp" id="0x7f030133" />
    <public type="attr" name="splitMinSmallestWidthDp" id="0x7f030134" />
    <public type="attr" name="splitMinWidthDp" id="0x7f030135" />
    <public type="attr" name="splitRatio" id="0x7f030136" />
    <public type="attr" name="splitTrack" id="0x7f030137" />
    <public type="attr" name="srcCompat" id="0x7f030138" />
    <public type="attr" name="stackFromEnd" id="0x7f030139" />
    <public type="attr" name="state_above_anchor" id="0x7f03013a" />
    <public type="attr" name="statusBarBackground" id="0x7f03013b" />
    <public type="attr" name="stickyPlaceholder" id="0x7f03013c" />
    <public type="attr" name="subMenuArrow" id="0x7f03013d" />
    <public type="attr" name="submitBackground" id="0x7f03013e" />
    <public type="attr" name="subtitle" id="0x7f03013f" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f030140" />
    <public type="attr" name="subtitleTextColor" id="0x7f030141" />
    <public type="attr" name="subtitleTextStyle" id="0x7f030142" />
    <public type="attr" name="suggestionRowLayout" id="0x7f030143" />
    <public type="attr" name="summary" id="0x7f030144" />
    <public type="attr" name="summaryOff" id="0x7f030145" />
    <public type="attr" name="summaryOn" id="0x7f030146" />
    <public type="attr" name="switchMinWidth" id="0x7f030147" />
    <public type="attr" name="switchPadding" id="0x7f030148" />
    <public type="attr" name="switchPreferenceCompatStyle" id="0x7f030149" />
    <public type="attr" name="switchPreferenceStyle" id="0x7f03014a" />
    <public type="attr" name="switchStyle" id="0x7f03014b" />
    <public type="attr" name="switchTextAppearance" id="0x7f03014c" />
    <public type="attr" name="switchTextOff" id="0x7f03014d" />
    <public type="attr" name="switchTextOn" id="0x7f03014e" />
    <public type="attr" name="tag" id="0x7f03014f" />
    <public type="attr" name="textAllCaps" id="0x7f030150" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f030151" />
    <public type="attr" name="textAppearanceListItem" id="0x7f030152" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f030153" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f030154" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f030155" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f030156" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f030157" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f030158" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f030159" />
    <public type="attr" name="textColorSearchUrl" id="0x7f03015a" />
    <public type="attr" name="textLocale" id="0x7f03015b" />
    <public type="attr" name="theme" id="0x7f03015c" />
    <public type="attr" name="thickness" id="0x7f03015d" />
    <public type="attr" name="thumbTextPadding" id="0x7f03015e" />
    <public type="attr" name="thumbTint" id="0x7f03015f" />
    <public type="attr" name="thumbTintMode" id="0x7f030160" />
    <public type="attr" name="tickMark" id="0x7f030161" />
    <public type="attr" name="tickMarkTint" id="0x7f030162" />
    <public type="attr" name="tickMarkTintMode" id="0x7f030163" />
    <public type="attr" name="tint" id="0x7f030164" />
    <public type="attr" name="tintMode" id="0x7f030165" />
    <public type="attr" name="title" id="0x7f030166" />
    <public type="attr" name="titleMargin" id="0x7f030167" />
    <public type="attr" name="titleMarginBottom" id="0x7f030168" />
    <public type="attr" name="titleMarginEnd" id="0x7f030169" />
    <public type="attr" name="titleMarginStart" id="0x7f03016a" />
    <public type="attr" name="titleMarginTop" id="0x7f03016b" />
    <public type="attr" name="titleMargins" id="0x7f03016c" />
    <public type="attr" name="titleTextAppearance" id="0x7f03016d" />
    <public type="attr" name="titleTextColor" id="0x7f03016e" />
    <public type="attr" name="titleTextStyle" id="0x7f03016f" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f030170" />
    <public type="attr" name="toolbarStyle" id="0x7f030171" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f030172" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f030173" />
    <public type="attr" name="tooltipText" id="0x7f030174" />
    <public type="attr" name="track" id="0x7f030175" />
    <public type="attr" name="trackTint" id="0x7f030176" />
    <public type="attr" name="trackTintMode" id="0x7f030177" />
    <public type="attr" name="ttcIndex" id="0x7f030178" />
    <public type="attr" name="updatesContinuously" id="0x7f030179" />
    <public type="attr" name="useSimpleSummaryProvider" id="0x7f03017a" />
    <public type="attr" name="viewInflaterClass" id="0x7f03017b" />
    <public type="attr" name="voiceIcon" id="0x7f03017c" />
    <public type="attr" name="widgetLayout" id="0x7f03017d" />
    <public type="attr" name="windowActionBar" id="0x7f03017e" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f03017f" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f030180" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f030181" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f030182" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f030183" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f030184" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f030185" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f030186" />
    <public type="attr" name="windowNoTitle" id="0x7f030187" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f040000" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f040001" />
    <public type="bool" name="config_materialPreferenceIconSpaceReserved" id="0x7f040002" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f050000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f050001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f050002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f050003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f050004" />
    <public type="color" name="abc_decor_view_status_guard" id="0x7f050005" />
    <public type="color" name="abc_decor_view_status_guard_light" id="0x7f050006" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f050007" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f050008" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f050009" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f05000a" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f05000b" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f05000c" />
    <public type="color" name="abc_search_url_text" id="0x7f05000d" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f05000e" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f05000f" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f050010" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f050011" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f050012" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f050013" />
    <public type="color" name="abc_tint_default" id="0x7f050014" />
    <public type="color" name="abc_tint_edittext" id="0x7f050015" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f050016" />
    <public type="color" name="abc_tint_spinner" id="0x7f050017" />
    <public type="color" name="abc_tint_switch_track" id="0x7f050018" />
    <public type="color" name="accent_material_dark" id="0x7f050019" />
    <public type="color" name="accent_material_light" id="0x7f05001a" />
    <public type="color" name="androidx_core_ripple_material_light" id="0x7f05001b" />
    <public type="color" name="androidx_core_secondary_text_default_material_light" id="0x7f05001c" />
    <public type="color" name="background_floating_material_dark" id="0x7f05001d" />
    <public type="color" name="background_floating_material_light" id="0x7f05001e" />
    <public type="color" name="background_material_dark" id="0x7f05001f" />
    <public type="color" name="background_material_light" id="0x7f050020" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f050021" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f050022" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f050023" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f050024" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f050025" />
    <public type="color" name="bright_foreground_material_light" id="0x7f050026" />
    <public type="color" name="browser_actions_bg_grey" id="0x7f050027" />
    <public type="color" name="browser_actions_divider_color" id="0x7f050028" />
    <public type="color" name="browser_actions_text_color" id="0x7f050029" />
    <public type="color" name="browser_actions_title_color" id="0x7f05002a" />
    <public type="color" name="button_material_dark" id="0x7f05002b" />
    <public type="color" name="button_material_light" id="0x7f05002c" />
    <public type="color" name="call_notification_answer_color" id="0x7f05002d" />
    <public type="color" name="call_notification_decline_color" id="0x7f05002e" />
    <public type="color" name="common_google_signin_btn_text_dark" id="0x7f05002f" />
    <public type="color" name="common_google_signin_btn_text_dark_default" id="0x7f050030" />
    <public type="color" name="common_google_signin_btn_text_dark_disabled" id="0x7f050031" />
    <public type="color" name="common_google_signin_btn_text_dark_focused" id="0x7f050032" />
    <public type="color" name="common_google_signin_btn_text_dark_pressed" id="0x7f050033" />
    <public type="color" name="common_google_signin_btn_text_light" id="0x7f050034" />
    <public type="color" name="common_google_signin_btn_text_light_default" id="0x7f050035" />
    <public type="color" name="common_google_signin_btn_text_light_disabled" id="0x7f050036" />
    <public type="color" name="common_google_signin_btn_text_light_focused" id="0x7f050037" />
    <public type="color" name="common_google_signin_btn_text_light_pressed" id="0x7f050038" />
    <public type="color" name="common_google_signin_btn_tint" id="0x7f050039" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f05003a" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f05003b" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f05003c" />
    <public type="color" name="dim_foreground_material_light" id="0x7f05003d" />
    <public type="color" name="error_color_material_dark" id="0x7f05003e" />
    <public type="color" name="error_color_material_light" id="0x7f05003f" />
    <public type="color" name="foreground_material_dark" id="0x7f050040" />
    <public type="color" name="foreground_material_light" id="0x7f050041" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f050042" />
    <public type="color" name="highlighted_text_material_light" id="0x7f050043" />
    <public type="color" name="material_blue_grey_800" id="0x7f050044" />
    <public type="color" name="material_blue_grey_900" id="0x7f050045" />
    <public type="color" name="material_blue_grey_950" id="0x7f050046" />
    <public type="color" name="material_deep_teal_200" id="0x7f050047" />
    <public type="color" name="material_deep_teal_500" id="0x7f050048" />
    <public type="color" name="material_grey_100" id="0x7f050049" />
    <public type="color" name="material_grey_300" id="0x7f05004a" />
    <public type="color" name="material_grey_50" id="0x7f05004b" />
    <public type="color" name="material_grey_600" id="0x7f05004c" />
    <public type="color" name="material_grey_800" id="0x7f05004d" />
    <public type="color" name="material_grey_850" id="0x7f05004e" />
    <public type="color" name="material_grey_900" id="0x7f05004f" />
    <public type="color" name="notification_action_color_filter" id="0x7f050050" />
    <public type="color" name="notification_icon_bg_color" id="0x7f050051" />
    <public type="color" name="preference_fallback_accent_color" id="0x7f050052" />
    <public type="color" name="primary_dark_material_dark" id="0x7f050053" />
    <public type="color" name="primary_dark_material_light" id="0x7f050054" />
    <public type="color" name="primary_material_dark" id="0x7f050055" />
    <public type="color" name="primary_material_light" id="0x7f050056" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f050057" />
    <public type="color" name="primary_text_default_material_light" id="0x7f050058" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f050059" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f05005a" />
    <public type="color" name="ripple_material_dark" id="0x7f05005b" />
    <public type="color" name="ripple_material_light" id="0x7f05005c" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f05005d" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f05005e" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f05005f" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f050060" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f050061" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f050062" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f050063" />
    <public type="color" name="switch_thumb_material_light" id="0x7f050064" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f050065" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f050066" />
    <public type="color" name="tooltip_background_dark" id="0x7f050067" />
    <public type="color" name="tooltip_background_light" id="0x7f050068" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f060000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f060001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f060002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f060003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f060004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f060005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f060006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f060007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f060008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f060009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f06000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f06000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f06000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f06000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f06000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f06000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f060010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f060011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f060012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f060013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f060014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f060015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f060016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f060017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f060018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f060019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f06001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f06001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f06001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f06001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f06001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f06001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f060020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f060021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f060022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f060023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f060024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f060025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f060026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f060027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f060028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f060029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f06002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f06002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f06002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f06002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f06002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f06002f" />
    <public type="dimen" name="abc_list_item_height_large_material" id="0x7f060030" />
    <public type="dimen" name="abc_list_item_height_material" id="0x7f060031" />
    <public type="dimen" name="abc_list_item_height_small_material" id="0x7f060032" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f060033" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f060034" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f060035" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f060036" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f060037" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f060038" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f060039" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f06003a" />
    <public type="dimen" name="abc_star_big" id="0x7f06003b" />
    <public type="dimen" name="abc_star_medium" id="0x7f06003c" />
    <public type="dimen" name="abc_star_small" id="0x7f06003d" />
    <public type="dimen" name="abc_switch_padding" id="0x7f06003e" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f06003f" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f060040" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f060041" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f060042" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f060043" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f060044" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f060045" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f060046" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f060047" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f060048" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f060049" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f06004a" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f06004b" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f06004c" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f06004d" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f06004e" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f06004f" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f060050" />
    <public type="dimen" name="browser_actions_context_menu_max_width" id="0x7f060051" />
    <public type="dimen" name="browser_actions_context_menu_min_padding" id="0x7f060052" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f060053" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f060054" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f060055" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f060056" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f060057" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f060058" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f060059" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f06005a" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f06005b" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f06005c" />
    <public type="dimen" name="fastscroll_margin" id="0x7f06005d" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f06005e" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f06005f" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f060060" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f060061" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f060062" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f060063" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f060064" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f060065" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f060066" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f060067" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f060068" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f060069" />
    <public type="dimen" name="notification_action_text_size" id="0x7f06006a" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f06006b" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f06006c" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f06006d" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f06006e" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f06006f" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f060070" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f060071" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f060072" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f060073" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f060074" />
    <public type="dimen" name="notification_subtext_size" id="0x7f060075" />
    <public type="dimen" name="notification_top_pad" id="0x7f060076" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f060077" />
    <public type="dimen" name="preference_dropdown_padding_start" id="0x7f060078" />
    <public type="dimen" name="preference_icon_minWidth" id="0x7f060079" />
    <public type="dimen" name="preference_seekbar_padding_horizontal" id="0x7f06007a" />
    <public type="dimen" name="preference_seekbar_padding_vertical" id="0x7f06007b" />
    <public type="dimen" name="preference_seekbar_value_minWidth" id="0x7f06007c" />
    <public type="dimen" name="preferences_detail_width" id="0x7f06007d" />
    <public type="dimen" name="preferences_header_width" id="0x7f06007e" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f06007f" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f060080" />
    <public type="dimen" name="tooltip_margin" id="0x7f060081" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f060082" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f060083" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f060084" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f060085" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f060086" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f070001" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f070002" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f070003" />
    <public type="drawable" name="abc_btn_check_material_anim" id="0x7f070004" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f070007" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f070008" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f070009" />
    <public type="drawable" name="abc_btn_radio_material_anim" id="0x7f07000a" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f07000f" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f070010" />
    <public type="drawable" name="abc_control_background_material" id="0x7f070012" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f070013" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f070014" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f070015" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f070016" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f070017" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f070019" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f07001a" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f07001b" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f07001c" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f07001d" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f07001e" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f07001f" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f070020" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f070021" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f070022" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f070023" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f070024" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f07002a" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f07002b" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f07002e" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f07002f" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f070032" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f070033" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f070034" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f07003a" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f07003b" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f07003c" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f07003e" />
    <public type="drawable" name="abc_star_black_48dp" id="0x7f07003f" />
    <public type="drawable" name="abc_star_half_black_48dp" id="0x7f070040" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f070041" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f070043" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f070045" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f07004d" />
    <public type="drawable" name="abc_vector_test" id="0x7f07004e" />
    <public type="drawable" name="btn_checkbox_checked_mtrl" id="0x7f07004f" />
    <public type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation" id="0x7f070050" />
    <public type="drawable" name="btn_checkbox_unchecked_mtrl" id="0x7f070051" />
    <public type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation" id="0x7f070052" />
    <public type="drawable" name="btn_radio_off_mtrl" id="0x7f070053" />
    <public type="drawable" name="btn_radio_off_to_on_mtrl_animation" id="0x7f070054" />
    <public type="drawable" name="btn_radio_on_mtrl" id="0x7f070055" />
    <public type="drawable" name="btn_radio_on_to_off_mtrl_animation" id="0x7f070056" />
    <public type="drawable" name="common_google_signin_btn_icon_dark" id="0x7f070058" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_focused" id="0x7f070059" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_normal" id="0x7f07005a" />
    <public type="drawable" name="common_google_signin_btn_icon_disabled" id="0x7f07005c" />
    <public type="drawable" name="common_google_signin_btn_icon_light" id="0x7f07005d" />
    <public type="drawable" name="common_google_signin_btn_icon_light_focused" id="0x7f07005e" />
    <public type="drawable" name="common_google_signin_btn_icon_light_normal" id="0x7f07005f" />
    <public type="drawable" name="common_google_signin_btn_text_dark" id="0x7f070061" />
    <public type="drawable" name="common_google_signin_btn_text_dark_focused" id="0x7f070062" />
    <public type="drawable" name="common_google_signin_btn_text_dark_normal" id="0x7f070063" />
    <public type="drawable" name="common_google_signin_btn_text_disabled" id="0x7f070065" />
    <public type="drawable" name="common_google_signin_btn_text_light" id="0x7f070066" />
    <public type="drawable" name="common_google_signin_btn_text_light_focused" id="0x7f070067" />
    <public type="drawable" name="common_google_signin_btn_text_light_normal" id="0x7f070068" />
    <public type="drawable" name="ic_arrow_down_24dp" id="0x7f07006c" />
    <public type="drawable" name="launch_background" id="0x7f070073" />
    <public type="drawable" name="notification_action_background" id="0x7f070074" />
    <public type="drawable" name="notification_bg" id="0x7f070075" />
    <public type="drawable" name="notification_bg_low" id="0x7f070076" />
    <public type="drawable" name="notification_icon_background" id="0x7f07007b" />
    <public type="drawable" name="notification_oversize_large_icon_bg" id="0x7f07007c" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f07007d" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f07007e" />
    <public type="drawable" name="notification_tile_bg" id="0x7f07007f" />
    <public type="drawable" name="preference_list_divider_material" id="0x7f070081" />
    <public type="drawable" name="test_level_drawable" id="0x7f070082" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f070083" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f070084" />
    <public type="id" name="ALT" id="0x7f080000" />
    <public type="id" name="CTRL" id="0x7f080001" />
    <public type="id" name="FUNCTION" id="0x7f080002" />
    <public type="id" name="META" id="0x7f080003" />
    <public type="id" name="SHIFT" id="0x7f080004" />
    <public type="id" name="SYM" id="0x7f080005" />
    <public type="id" name="accessibility_action_clickable_span" id="0x7f080006" />
    <public type="id" name="accessibility_custom_action_0" id="0x7f080007" />
    <public type="id" name="accessibility_custom_action_1" id="0x7f080008" />
    <public type="id" name="accessibility_custom_action_10" id="0x7f080009" />
    <public type="id" name="accessibility_custom_action_11" id="0x7f08000a" />
    <public type="id" name="accessibility_custom_action_12" id="0x7f08000b" />
    <public type="id" name="accessibility_custom_action_13" id="0x7f08000c" />
    <public type="id" name="accessibility_custom_action_14" id="0x7f08000d" />
    <public type="id" name="accessibility_custom_action_15" id="0x7f08000e" />
    <public type="id" name="accessibility_custom_action_16" id="0x7f08000f" />
    <public type="id" name="accessibility_custom_action_17" id="0x7f080010" />
    <public type="id" name="accessibility_custom_action_18" id="0x7f080011" />
    <public type="id" name="accessibility_custom_action_19" id="0x7f080012" />
    <public type="id" name="accessibility_custom_action_2" id="0x7f080013" />
    <public type="id" name="accessibility_custom_action_20" id="0x7f080014" />
    <public type="id" name="accessibility_custom_action_21" id="0x7f080015" />
    <public type="id" name="accessibility_custom_action_22" id="0x7f080016" />
    <public type="id" name="accessibility_custom_action_23" id="0x7f080017" />
    <public type="id" name="accessibility_custom_action_24" id="0x7f080018" />
    <public type="id" name="accessibility_custom_action_25" id="0x7f080019" />
    <public type="id" name="accessibility_custom_action_26" id="0x7f08001a" />
    <public type="id" name="accessibility_custom_action_27" id="0x7f08001b" />
    <public type="id" name="accessibility_custom_action_28" id="0x7f08001c" />
    <public type="id" name="accessibility_custom_action_29" id="0x7f08001d" />
    <public type="id" name="accessibility_custom_action_3" id="0x7f08001e" />
    <public type="id" name="accessibility_custom_action_30" id="0x7f08001f" />
    <public type="id" name="accessibility_custom_action_31" id="0x7f080020" />
    <public type="id" name="accessibility_custom_action_4" id="0x7f080021" />
    <public type="id" name="accessibility_custom_action_5" id="0x7f080022" />
    <public type="id" name="accessibility_custom_action_6" id="0x7f080023" />
    <public type="id" name="accessibility_custom_action_7" id="0x7f080024" />
    <public type="id" name="accessibility_custom_action_8" id="0x7f080025" />
    <public type="id" name="accessibility_custom_action_9" id="0x7f080026" />
    <public type="id" name="action_bar" id="0x7f080027" />
    <public type="id" name="action_bar_activity_content" id="0x7f080028" />
    <public type="id" name="action_bar_container" id="0x7f080029" />
    <public type="id" name="action_bar_root" id="0x7f08002a" />
    <public type="id" name="action_bar_spinner" id="0x7f08002b" />
    <public type="id" name="action_bar_subtitle" id="0x7f08002c" />
    <public type="id" name="action_bar_title" id="0x7f08002d" />
    <public type="id" name="action_container" id="0x7f08002e" />
    <public type="id" name="action_context_bar" id="0x7f08002f" />
    <public type="id" name="action_divider" id="0x7f080030" />
    <public type="id" name="action_image" id="0x7f080031" />
    <public type="id" name="action_menu_divider" id="0x7f080032" />
    <public type="id" name="action_menu_presenter" id="0x7f080033" />
    <public type="id" name="action_mode_bar" id="0x7f080034" />
    <public type="id" name="action_mode_bar_stub" id="0x7f080035" />
    <public type="id" name="action_mode_close_button" id="0x7f080036" />
    <public type="id" name="action_text" id="0x7f080037" />
    <public type="id" name="actions" id="0x7f080038" />
    <public type="id" name="activity_chooser_view_content" id="0x7f080039" />
    <public type="id" name="add" id="0x7f08003a" />
    <public type="id" name="adjacent" id="0x7f08003b" />
    <public type="id" name="adjust_height" id="0x7f08003c" />
    <public type="id" name="adjust_width" id="0x7f08003d" />
    <public type="id" name="alertTitle" id="0x7f08003e" />
    <public type="id" name="all" id="0x7f08003f" />
    <public type="id" name="always" id="0x7f080040" />
    <public type="id" name="alwaysAllow" id="0x7f080041" />
    <public type="id" name="alwaysDisallow" id="0x7f080042" />
    <public type="id" name="androidx_window_activity_scope" id="0x7f080043" />
    <public type="id" name="async" id="0x7f080044" />
    <public type="id" name="auto" id="0x7f080045" />
    <public type="id" name="beginning" id="0x7f080046" />
    <public type="id" name="blocking" id="0x7f080047" />
    <public type="id" name="bottom" id="0x7f080048" />
    <public type="id" name="bottomToTop" id="0x7f080049" />
    <public type="id" name="browser_actions_header_text" id="0x7f08004a" />
    <public type="id" name="browser_actions_menu_item_icon" id="0x7f08004b" />
    <public type="id" name="browser_actions_menu_item_text" id="0x7f08004c" />
    <public type="id" name="browser_actions_menu_items" id="0x7f08004d" />
    <public type="id" name="browser_actions_menu_view" id="0x7f08004e" />
    <public type="id" name="buttonPanel" id="0x7f08004f" />
    <public type="id" name="center" id="0x7f080050" />
    <public type="id" name="center_horizontal" id="0x7f080051" />
    <public type="id" name="center_vertical" id="0x7f080052" />
    <public type="id" name="checkbox" id="0x7f080053" />
    <public type="id" name="checked" id="0x7f080054" />
    <public type="id" name="chronometer" id="0x7f080055" />
    <public type="id" name="clip_horizontal" id="0x7f080056" />
    <public type="id" name="clip_vertical" id="0x7f080057" />
    <public type="id" name="collapseActionView" id="0x7f080058" />
    <public type="id" name="content" id="0x7f080059" />
    <public type="id" name="contentPanel" id="0x7f08005a" />
    <public type="id" name="custom" id="0x7f08005b" />
    <public type="id" name="customPanel" id="0x7f08005c" />
    <public type="id" name="dark" id="0x7f08005d" />
    <public type="id" name="decor_content_parent" id="0x7f08005e" />
    <public type="id" name="default_activity_button" id="0x7f08005f" />
    <public type="id" name="dialog_button" id="0x7f080060" />
    <public type="id" name="disableHome" id="0x7f080061" />
    <public type="id" name="edit_query" id="0x7f080062" />
    <public type="id" name="edit_text_id" id="0x7f080063" />
    <public type="id" name="end" id="0x7f080064" />
    <public type="id" name="expand_activities_button" id="0x7f080065" />
    <public type="id" name="expanded_menu" id="0x7f080066" />
    <public type="id" name="fill" id="0x7f080067" />
    <public type="id" name="fill_horizontal" id="0x7f080068" />
    <public type="id" name="fill_vertical" id="0x7f080069" />
    <public type="id" name="forever" id="0x7f08006a" />
    <public type="id" name="fragment_container_view_tag" id="0x7f08006b" />
    <public type="id" name="ghost_view" id="0x7f08006c" />
    <public type="id" name="ghost_view_holder" id="0x7f08006d" />
    <public type="id" name="group_divider" id="0x7f08006e" />
    <public type="id" name="hide_ime_id" id="0x7f08006f" />
    <public type="id" name="home" id="0x7f080070" />
    <public type="id" name="homeAsUp" id="0x7f080071" />
    <public type="id" name="icon" id="0x7f080072" />
    <public type="id" name="icon_frame" id="0x7f080073" />
    <public type="id" name="icon_group" id="0x7f080074" />
    <public type="id" name="icon_only" id="0x7f080075" />
    <public type="id" name="ifRoom" id="0x7f080076" />
    <public type="id" name="image" id="0x7f080077" />
    <public type="id" name="info" id="0x7f080078" />
    <public type="id" name="italic" id="0x7f080079" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f08007a" />
    <public type="id" name="left" id="0x7f08007b" />
    <public type="id" name="light" id="0x7f08007c" />
    <public type="id" name="line1" id="0x7f08007d" />
    <public type="id" name="line3" id="0x7f08007e" />
    <public type="id" name="listMode" id="0x7f08007f" />
    <public type="id" name="list_item" id="0x7f080080" />
    <public type="id" name="locale" id="0x7f080081" />
    <public type="id" name="ltr" id="0x7f080082" />
    <public type="id" name="message" id="0x7f080083" />
    <public type="id" name="middle" id="0x7f080084" />
    <public type="id" name="multiply" id="0x7f080085" />
    <public type="id" name="never" id="0x7f080086" />
    <public type="id" name="none" id="0x7f080087" />
    <public type="id" name="normal" id="0x7f080088" />
    <public type="id" name="notification_background" id="0x7f080089" />
    <public type="id" name="notification_main_column" id="0x7f08008a" />
    <public type="id" name="notification_main_column_container" id="0x7f08008b" />
    <public type="id" name="off" id="0x7f08008c" />
    <public type="id" name="on" id="0x7f08008d" />
    <public type="id" name="parentPanel" id="0x7f08008e" />
    <public type="id" name="parent_matrix" id="0x7f08008f" />
    <public type="id" name="preferences_detail" id="0x7f080090" />
    <public type="id" name="preferences_header" id="0x7f080091" />
    <public type="id" name="preferences_sliding_pane_layout" id="0x7f080092" />
    <public type="id" name="progress_circular" id="0x7f080093" />
    <public type="id" name="progress_horizontal" id="0x7f080094" />
    <public type="id" name="radio" id="0x7f080095" />
    <public type="id" name="recycler_view" id="0x7f080096" />
    <public type="id" name="report_drawn" id="0x7f080097" />
    <public type="id" name="right" id="0x7f080098" />
    <public type="id" name="right_icon" id="0x7f080099" />
    <public type="id" name="right_side" id="0x7f08009a" />
    <public type="id" name="rtl" id="0x7f08009b" />
    <public type="id" name="save_non_transition_alpha" id="0x7f08009c" />
    <public type="id" name="save_overlay_view" id="0x7f08009d" />
    <public type="id" name="screen" id="0x7f08009e" />
    <public type="id" name="scrollIndicatorDown" id="0x7f08009f" />
    <public type="id" name="scrollIndicatorUp" id="0x7f0800a0" />
    <public type="id" name="scrollView" id="0x7f0800a1" />
    <public type="id" name="search_badge" id="0x7f0800a2" />
    <public type="id" name="search_bar" id="0x7f0800a3" />
    <public type="id" name="search_button" id="0x7f0800a4" />
    <public type="id" name="search_close_btn" id="0x7f0800a5" />
    <public type="id" name="search_edit_frame" id="0x7f0800a6" />
    <public type="id" name="search_go_btn" id="0x7f0800a7" />
    <public type="id" name="search_mag_icon" id="0x7f0800a8" />
    <public type="id" name="search_plate" id="0x7f0800a9" />
    <public type="id" name="search_src_text" id="0x7f0800aa" />
    <public type="id" name="search_voice_btn" id="0x7f0800ab" />
    <public type="id" name="seekbar" id="0x7f0800ac" />
    <public type="id" name="seekbar_value" id="0x7f0800ad" />
    <public type="id" name="select_dialog_listview" id="0x7f0800ae" />
    <public type="id" name="shortcut" id="0x7f0800af" />
    <public type="id" name="showCustom" id="0x7f0800b0" />
    <public type="id" name="showHome" id="0x7f0800b1" />
    <public type="id" name="showTitle" id="0x7f0800b2" />
    <public type="id" name="spacer" id="0x7f0800b3" />
    <public type="id" name="special_effects_controller_view_tag" id="0x7f0800b4" />
    <public type="id" name="spinner" id="0x7f0800b5" />
    <public type="id" name="split_action_bar" id="0x7f0800b6" />
    <public type="id" name="src_atop" id="0x7f0800b7" />
    <public type="id" name="src_in" id="0x7f0800b8" />
    <public type="id" name="src_over" id="0x7f0800b9" />
    <public type="id" name="standard" id="0x7f0800ba" />
    <public type="id" name="start" id="0x7f0800bb" />
    <public type="id" name="submenuarrow" id="0x7f0800bc" />
    <public type="id" name="submit_area" id="0x7f0800bd" />
    <public type="id" name="switchWidget" id="0x7f0800be" />
    <public type="id" name="tabMode" id="0x7f0800bf" />
    <public type="id" name="tag_accessibility_actions" id="0x7f0800c0" />
    <public type="id" name="tag_accessibility_clickable_spans" id="0x7f0800c1" />
    <public type="id" name="tag_accessibility_heading" id="0x7f0800c2" />
    <public type="id" name="tag_accessibility_pane_title" id="0x7f0800c3" />
    <public type="id" name="tag_on_apply_window_listener" id="0x7f0800c4" />
    <public type="id" name="tag_on_receive_content_listener" id="0x7f0800c5" />
    <public type="id" name="tag_on_receive_content_mime_types" id="0x7f0800c6" />
    <public type="id" name="tag_screen_reader_focusable" id="0x7f0800c7" />
    <public type="id" name="tag_state_description" id="0x7f0800c8" />
    <public type="id" name="tag_transition_group" id="0x7f0800c9" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f0800ca" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f0800cb" />
    <public type="id" name="tag_window_insets_animation_callback" id="0x7f0800cc" />
    <public type="id" name="text" id="0x7f0800cd" />
    <public type="id" name="text2" id="0x7f0800ce" />
    <public type="id" name="textSpacerNoButtons" id="0x7f0800cf" />
    <public type="id" name="textSpacerNoTitle" id="0x7f0800d0" />
    <public type="id" name="time" id="0x7f0800d1" />
    <public type="id" name="title" id="0x7f0800d2" />
    <public type="id" name="titleDividerNoCustom" id="0x7f0800d3" />
    <public type="id" name="title_template" id="0x7f0800d4" />
    <public type="id" name="top" id="0x7f0800d5" />
    <public type="id" name="topPanel" id="0x7f0800d6" />
    <public type="id" name="topToBottom" id="0x7f0800d7" />
    <public type="id" name="transition_current_scene" id="0x7f0800d8" />
    <public type="id" name="transition_layout_save" id="0x7f0800d9" />
    <public type="id" name="transition_position" id="0x7f0800da" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f0800db" />
    <public type="id" name="transition_transform" id="0x7f0800dc" />
    <public type="id" name="unchecked" id="0x7f0800dd" />
    <public type="id" name="uniform" id="0x7f0800de" />
    <public type="id" name="up" id="0x7f0800df" />
    <public type="id" name="useLogo" id="0x7f0800e0" />
    <public type="id" name="view_tree_lifecycle_owner" id="0x7f0800e1" />
    <public type="id" name="view_tree_on_back_pressed_dispatcher_owner" id="0x7f0800e2" />
    <public type="id" name="view_tree_saved_state_registry_owner" id="0x7f0800e3" />
    <public type="id" name="view_tree_view_model_store_owner" id="0x7f0800e4" />
    <public type="id" name="visible_removing_fragment_view_tag" id="0x7f0800e5" />
    <public type="id" name="wide" id="0x7f0800e6" />
    <public type="id" name="withText" id="0x7f0800e7" />
    <public type="id" name="wrap_content" id="0x7f0800e8" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f090000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f090001" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f090002" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f090003" />
    <public type="integer" name="google_play_services_version" id="0x7f090004" />
    <public type="integer" name="preferences_detail_pane_weight" id="0x7f090005" />
    <public type="integer" name="preferences_header_pane_weight" id="0x7f090006" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f090007" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_0" id="0x7f0a0000" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_1" id="0x7f0a0001" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_0" id="0x7f0a0002" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_1" id="0x7f0a0003" />
    <public type="interpolator" name="btn_radio_to_off_mtrl_animation_interpolator_0" id="0x7f0a0004" />
    <public type="interpolator" name="btn_radio_to_on_mtrl_animation_interpolator_0" id="0x7f0a0005" />
    <public type="interpolator" name="fast_out_slow_in" id="0x7f0a0006" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0b0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0b0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0b0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0b0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0b0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0b0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0b0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0b0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0b0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0b0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0b000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0b000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0b000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0b000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0b000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0b000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0b0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0b0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0b0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0b0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0b0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0b0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0b0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0b0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0b0018" />
    <public type="layout" name="abc_search_view" id="0x7f0b0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0b001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0b001b" />
    <public type="layout" name="browser_actions_context_menu_page" id="0x7f0b001c" />
    <public type="layout" name="browser_actions_context_menu_row" id="0x7f0b001d" />
    <public type="layout" name="custom_dialog" id="0x7f0b001e" />
    <public type="layout" name="expand_button" id="0x7f0b001f" />
    <public type="layout" name="image_frame" id="0x7f0b0020" />
    <public type="layout" name="ime_base_split_test_activity" id="0x7f0b0021" />
    <public type="layout" name="ime_secondary_split_test_activity" id="0x7f0b0022" />
    <public type="layout" name="notification_action" id="0x7f0b0023" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0b0024" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0b0025" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0b0026" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0b0027" />
    <public type="layout" name="notification_template_part_time" id="0x7f0b0028" />
    <public type="layout" name="preference" id="0x7f0b0029" />
    <public type="layout" name="preference_category" id="0x7f0b002a" />
    <public type="layout" name="preference_category_material" id="0x7f0b002b" />
    <public type="layout" name="preference_dialog_edittext" id="0x7f0b002c" />
    <public type="layout" name="preference_dropdown" id="0x7f0b002d" />
    <public type="layout" name="preference_dropdown_material" id="0x7f0b002e" />
    <public type="layout" name="preference_information" id="0x7f0b002f" />
    <public type="layout" name="preference_information_material" id="0x7f0b0030" />
    <public type="layout" name="preference_list_fragment" id="0x7f0b0031" />
    <public type="layout" name="preference_material" id="0x7f0b0032" />
    <public type="layout" name="preference_recyclerview" id="0x7f0b0033" />
    <public type="layout" name="preference_widget_checkbox" id="0x7f0b0034" />
    <public type="layout" name="preference_widget_seekbar" id="0x7f0b0035" />
    <public type="layout" name="preference_widget_seekbar_material" id="0x7f0b0036" />
    <public type="layout" name="preference_widget_switch" id="0x7f0b0037" />
    <public type="layout" name="preference_widget_switch_compat" id="0x7f0b0038" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0b0039" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0b003a" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0b003b" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0b003c" />
    <public type="mipmap" name="ic_launcher" id="0x7f0c0000" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f0d0000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f0d0001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f0d0002" />
    <public type="string" name="abc_action_mode_done" id="0x7f0d0003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f0d0004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f0d0005" />
    <public type="string" name="abc_capital_off" id="0x7f0d0006" />
    <public type="string" name="abc_capital_on" id="0x7f0d0007" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f0d0008" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f0d0009" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f0d000a" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f0d000b" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f0d000c" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f0d000d" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f0d000e" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f0d000f" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f0d0010" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f0d0011" />
    <public type="string" name="abc_search_hint" id="0x7f0d0012" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f0d0013" />
    <public type="string" name="abc_searchview_description_query" id="0x7f0d0014" />
    <public type="string" name="abc_searchview_description_search" id="0x7f0d0015" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f0d0016" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f0d0017" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f0d0018" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f0d0019" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f0d001a" />
    <public type="string" name="androidx_startup" id="0x7f0d001b" />
    <public type="string" name="call_notification_answer_action" id="0x7f0d001c" />
    <public type="string" name="call_notification_answer_video_action" id="0x7f0d001d" />
    <public type="string" name="call_notification_decline_action" id="0x7f0d001e" />
    <public type="string" name="call_notification_hang_up_action" id="0x7f0d001f" />
    <public type="string" name="call_notification_incoming_text" id="0x7f0d0020" />
    <public type="string" name="call_notification_ongoing_text" id="0x7f0d0021" />
    <public type="string" name="call_notification_screening_text" id="0x7f0d0022" />
    <public type="string" name="common_google_play_services_enable_button" id="0x7f0d0023" />
    <public type="string" name="common_google_play_services_enable_text" id="0x7f0d0024" />
    <public type="string" name="common_google_play_services_enable_title" id="0x7f0d0025" />
    <public type="string" name="common_google_play_services_install_button" id="0x7f0d0026" />
    <public type="string" name="common_google_play_services_install_text" id="0x7f0d0027" />
    <public type="string" name="common_google_play_services_install_title" id="0x7f0d0028" />
    <public type="string" name="common_google_play_services_notification_channel_name" id="0x7f0d0029" />
    <public type="string" name="common_google_play_services_notification_ticker" id="0x7f0d002a" />
    <public type="string" name="common_google_play_services_unknown_issue" id="0x7f0d002b" />
    <public type="string" name="common_google_play_services_unsupported_text" id="0x7f0d002c" />
    <public type="string" name="common_google_play_services_update_button" id="0x7f0d002d" />
    <public type="string" name="common_google_play_services_update_text" id="0x7f0d002e" />
    <public type="string" name="common_google_play_services_update_title" id="0x7f0d002f" />
    <public type="string" name="common_google_play_services_updating_text" id="0x7f0d0030" />
    <public type="string" name="common_google_play_services_wear_update_text" id="0x7f0d0031" />
    <public type="string" name="common_open_on_phone" id="0x7f0d0032" />
    <public type="string" name="common_signin_button_text" id="0x7f0d0033" />
    <public type="string" name="common_signin_button_text_long" id="0x7f0d0034" />
    <public type="string" name="copy" id="0x7f0d0035" />
    <public type="string" name="copy_toast_msg" id="0x7f0d0036" />
    <public type="string" name="expand_button_title" id="0x7f0d0037" />
    <public type="string" name="fallback_menu_item_copy_link" id="0x7f0d0038" />
    <public type="string" name="fallback_menu_item_open_in_browser" id="0x7f0d0039" />
    <public type="string" name="fallback_menu_item_share_link" id="0x7f0d003a" />
    <public type="string" name="not_set" id="0x7f0d003b" />
    <public type="string" name="preference_copied" id="0x7f0d003c" />
    <public type="string" name="search_menu_title" id="0x7f0d003d" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f0d003e" />
    <public type="string" name="summary_collapsed_preference_list" id="0x7f0d003f" />
    <public type="string" name="v7_preference_off" id="0x7f0d0040" />
    <public type="string" name="v7_preference_on" id="0x7f0d0041" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f0e0000" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f0e0001" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f0e0002" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f0e0003" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f0e0004" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f0e0005" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f0e0006" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f0e0007" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f0e0008" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f0e0009" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f0e000a" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f0e000b" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f0e000c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f0e000d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f0e000e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f0e000f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f0e0010" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f0e0011" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f0e0012" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f0e0013" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f0e0014" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f0e0015" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f0e0016" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f0e0017" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f0e0018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f0e0019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f0e001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f0e001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f0e001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f0e001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f0e001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f0e001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f0e0020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f0e0021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f0e0022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f0e0023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f0e0024" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f0e0025" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f0e0026" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f0e0027" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f0e0028" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f0e0029" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f0e002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f0e002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f0e002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f0e002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f0e002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f0e002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f0e0030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f0e0031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f0e0032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f0e0033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f0e0034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f0e0035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f0e0036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f0e0037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f0e0038" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f0e0039" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f0e003a" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f0e003b" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f0e003c" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f0e003d" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f0e003e" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f0e003f" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f0e0040" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f0e0041" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f0e0042" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f0e0043" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f0e0044" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f0e0045" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f0e0046" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f0e0047" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f0e0048" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f0e0049" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f0e004a" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f0e004b" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f0e004c" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f0e004d" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f0e004e" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f0e004f" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f0e0050" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f0e0051" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f0e0052" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f0e0053" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f0e0054" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f0e0055" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f0e0056" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f0e0057" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f0e0058" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f0e0059" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f0e005a" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f0e005b" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f0e005c" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f0e005d" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f0e005e" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f0e005f" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f0e0060" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f0e0061" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f0e0062" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f0e0063" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f0e0064" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f0e0065" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f0e0066" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f0e0067" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f0e0068" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f0e0069" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f0e006a" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f0e006b" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f0e006c" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f0e006d" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f0e006e" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f0e006f" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f0e0070" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f0e0071" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f0e0072" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f0e0073" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f0e0074" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f0e0075" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f0e0076" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f0e0077" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f0e0078" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f0e0079" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f0e007a" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f0e007b" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f0e007c" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f0e007d" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f0e007e" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f0e007f" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f0e0080" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f0e0081" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f0e0082" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f0e0083" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f0e0084" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f0e0085" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f0e0086" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f0e0087" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f0e0088" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f0e0089" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f0e008a" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f0e008b" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f0e008c" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f0e008d" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f0e008e" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f0e008f" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f0e0090" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f0e0091" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f0e0092" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f0e0093" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f0e0094" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f0e0095" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f0e0096" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f0e0097" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f0e0098" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f0e0099" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f0e009a" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f0e009b" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f0e009c" />
    <public type="style" name="Base.Widget.AppCompat.TextView" id="0x7f0e009d" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f0e009e" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f0e009f" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f0e00a0" />
    <public type="style" name="BasePreferenceThemeOverlay" id="0x7f0e00a1" />
    <public type="style" name="LaunchTheme" id="0x7f0e00a2" />
    <public type="style" name="NormalTheme" id="0x7f0e00a3" />
    <public type="style" name="Platform.AppCompat" id="0x7f0e00a4" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f0e00a5" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f0e00a6" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f0e00a7" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f0e00a8" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f0e00a9" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f0e00aa" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f0e00ab" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f0e00ac" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f0e00ad" />
    <public type="style" name="Preference" id="0x7f0e00ae" />
    <public type="style" name="Preference.Category" id="0x7f0e00af" />
    <public type="style" name="Preference.Category.Material" id="0x7f0e00b0" />
    <public type="style" name="Preference.CheckBoxPreference" id="0x7f0e00b1" />
    <public type="style" name="Preference.CheckBoxPreference.Material" id="0x7f0e00b2" />
    <public type="style" name="Preference.DialogPreference" id="0x7f0e00b3" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference" id="0x7f0e00b4" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference.Material" id="0x7f0e00b5" />
    <public type="style" name="Preference.DialogPreference.Material" id="0x7f0e00b6" />
    <public type="style" name="Preference.DropDown" id="0x7f0e00b7" />
    <public type="style" name="Preference.DropDown.Material" id="0x7f0e00b8" />
    <public type="style" name="Preference.Information" id="0x7f0e00b9" />
    <public type="style" name="Preference.Information.Material" id="0x7f0e00ba" />
    <public type="style" name="Preference.Material" id="0x7f0e00bb" />
    <public type="style" name="Preference.PreferenceScreen" id="0x7f0e00bc" />
    <public type="style" name="Preference.PreferenceScreen.Material" id="0x7f0e00bd" />
    <public type="style" name="Preference.SeekBarPreference" id="0x7f0e00be" />
    <public type="style" name="Preference.SeekBarPreference.Material" id="0x7f0e00bf" />
    <public type="style" name="Preference.SwitchPreference" id="0x7f0e00c0" />
    <public type="style" name="Preference.SwitchPreference.Material" id="0x7f0e00c1" />
    <public type="style" name="Preference.SwitchPreferenceCompat" id="0x7f0e00c2" />
    <public type="style" name="Preference.SwitchPreferenceCompat.Material" id="0x7f0e00c3" />
    <public type="style" name="PreferenceCategoryTitleTextStyle" id="0x7f0e00c4" />
    <public type="style" name="PreferenceFragment" id="0x7f0e00c5" />
    <public type="style" name="PreferenceFragment.Material" id="0x7f0e00c6" />
    <public type="style" name="PreferenceFragmentList" id="0x7f0e00c7" />
    <public type="style" name="PreferenceFragmentList.Material" id="0x7f0e00c8" />
    <public type="style" name="PreferenceSummaryTextStyle" id="0x7f0e00c9" />
    <public type="style" name="PreferenceThemeOverlay" id="0x7f0e00ca" />
    <public type="style" name="PreferenceThemeOverlay.v14" id="0x7f0e00cb" />
    <public type="style" name="PreferenceThemeOverlay.v14.Material" id="0x7f0e00cc" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f0e00cd" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f0e00ce" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f0e00cf" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f0e00d0" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f0e00d1" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f0e00d2" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f0e00d3" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f0e00d4" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f0e00d5" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f0e00d6" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f0e00d7" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f0e00d8" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f0e00d9" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f0e00da" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f0e00db" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f0e00dc" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f0e00dd" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f0e00de" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f0e00df" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f0e00e0" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f0e00e1" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f0e00e2" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f0e00e3" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f0e00e4" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f0e00e5" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f0e00e6" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f0e00e7" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f0e00e8" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f0e00e9" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f0e00ea" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f0e00eb" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f0e00ec" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f0e00ed" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f0e00ee" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f0e00ef" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f0e00f0" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f0e00f1" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f0e00f2" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f0e00f3" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f0e00f4" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f0e00f5" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f0e00f6" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f0e00f7" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f0e00f8" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f0e00f9" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f0e00fa" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f0e00fb" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f0e00fc" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f0e00fd" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f0e00fe" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f0e00ff" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f0e0100" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f0e0101" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f0e0102" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f0e0103" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f0e0104" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f0e0105" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f0e0106" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f0e0107" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f0e0108" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f0e0109" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f0e010a" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f0e010b" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f0e010c" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f0e010d" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f0e010e" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f0e010f" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f0e0110" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f0e0111" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f0e0112" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f0e0113" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f0e0114" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f0e0115" />
    <public type="style" name="Theme.AppCompat" id="0x7f0e0116" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f0e0117" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f0e0118" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f0e0119" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f0e011a" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f0e011b" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f0e011c" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f0e011d" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f0e011e" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f0e011f" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f0e0120" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f0e0121" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f0e0122" />
    <public type="style" name="Theme.AppCompat.Empty" id="0x7f0e0123" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f0e0124" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f0e0125" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f0e0126" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f0e0127" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f0e0128" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f0e0129" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f0e012a" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f0e012b" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f0e012c" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f0e012d" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f0e012e" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f0e012f" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight" id="0x7f0e0130" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight.ActionBar" id="0x7f0e0131" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f0e0132" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f0e0133" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f0e0134" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f0e0135" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f0e0136" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f0e0137" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f0e0138" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f0e0139" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f0e013a" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f0e013b" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f0e013c" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f0e013d" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f0e013e" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f0e013f" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f0e0140" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f0e0141" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f0e0142" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f0e0143" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f0e0144" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f0e0145" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f0e0146" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f0e0147" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f0e0148" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f0e0149" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f0e014a" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f0e014b" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f0e014c" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f0e014d" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f0e014e" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f0e014f" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f0e0150" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f0e0151" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f0e0152" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f0e0153" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f0e0154" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f0e0155" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f0e0156" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f0e0157" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f0e0158" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f0e0159" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f0e015a" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f0e015b" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f0e015c" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f0e015d" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f0e015e" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f0e015f" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f0e0160" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f0e0161" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f0e0162" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f0e0163" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f0e0164" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f0e0165" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f0e0166" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f0e0167" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f0e0168" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f0e0169" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f0e016a" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f0e016b" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f0e016c" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f0e016d" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f0e016e" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f0e016f" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f0e0170" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f0e0171" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f0e0172" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f0e0173" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f0e0174" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f0e0175" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f0e0176" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f0e0177" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f0e0178" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f0e0179" />
    <public type="style" name="Widget.AppCompat.TextView" id="0x7f0e017a" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f0e017b" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f0e017c" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f0e017d" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f0e017e" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f0e017f" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f0e0180" />
    <public type="xml" name="flutter_image_picker_file_paths" id="0x7f100000" />
    <public type="xml" name="image_share_filepaths" id="0x7f100001" />
    <public type="xml" name="splits0" id="0x7f100002" />
</resources>
