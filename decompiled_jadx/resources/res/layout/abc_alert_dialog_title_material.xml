<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/topPanel"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:gravity="start|center_vertical"
        android:orientation="horizontal"
        android:id="@+id/title_template"
        android:paddingLeft="?attr/dialogPreferredPadding"
        android:paddingTop="@dimen/abc_dialog_padding_top_material"
        android:paddingRight="?attr/dialogPreferredPadding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@android:id/icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginRight="8dp"
            android:src="@null"
            android:scaleType="fitCenter"
            android:layout_marginEnd="8dp"/>
        <androidx.appcompat.widget.DialogTitle
            android:ellipsize="end"
            android:layout_gravity="start"
            android:id="@+id/alertTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textAlignment="viewStart"
            style="?android:attr/windowTitleStyle"/>
    </LinearLayout>
    <android.widget.Space
        android:id="@+id/titleDividerNoCustom"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="@dimen/abc_dialog_title_divider_material"/>
</LinearLayout>
