<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_vertical"
    android:paddingLeft="?android:attr/listPreferredItemPaddingLeft"
    android:paddingRight="?android:attr/listPreferredItemPaddingRight"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?android:attr/listPreferredItemHeightSmall"
    android:paddingStart="?android:attr/listPreferredItemPaddingStart"
    android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">
    <LinearLayout
        android:gravity="start|center_vertical"
        android:orientation="horizontal"
        android:id="@+id/icon_frame"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minWidth="58dp">
        <ImageView
            android:id="@android:id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:layout_marginEnd="8dp"/>
    </LinearLayout>
    <RelativeLayout
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">
        <TextView
            android:textAppearance="?android:attr/textAppearanceListItem"
            android:id="@android:id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"/>
        <TextView
            android:textColor="?android:attr/textColorSecondary"
            android:id="@android:id/summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="10"
            android:layout_below="@android:id/title"
            android:layout_alignLeft="@android:id/title"
            android:layout_alignStart="@android:id/title"
            style="@style/PreferenceSummaryTextStyle"/>
    </RelativeLayout>
    <LinearLayout
        android:gravity="end|center_vertical"
        android:orientation="vertical"
        android:id="@android:id/widget_frame"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minWidth="58dp"/>
</LinearLayout>
