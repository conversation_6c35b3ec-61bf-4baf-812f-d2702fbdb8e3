<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="17"
    android:versionName="2.0.4"
    android:compileSdkVersion="35"
    android:compileSdkVersionCodename="15"
    android:requiredSplitTypes="base__abi,base__density"
    android:splitTypes=""
    package="com.sw.android.seal_project"
    platformBuildVersionCode="35"
    platformBuildVersionName="15">
    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="35"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
    <uses-feature android:name="android.hardware.camera.any"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"/>
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <permission
        android:name="com.sw.android.seal_project.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature"/>
    <uses-permission android:name="com.sw.android.seal_project.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"/>
    <application
        android:label="V SEAL"
        android:icon="@mipmap/ic_launcher"
        android:name="android.app.Application"
        android:extractNativeLibs="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory">
        <activity
            android:theme="@style/LaunchTheme"
            android:name="com.sw.android.seal_project.MainActivity"
            android:exported="true"
            android:taskAffinity=""
            android:launchMode="singleTop"
            android:configChanges="fontScale|layoutDirection|density|smallestScreenSize|screenSize|uiMode|screenLayout|orientation|keyboardHidden|keyboard|locale"
            android:windowSoftInputMode="adjustResize"
            android:hardwareAccelerated="true">
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <meta-data
            android:name="flutterEmbedding"
            android:value="2"/>
        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false">
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider"/>
        </service>
        <service
            android:name="com.baseflow.geolocator.GeolocatorLocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location"/>
        <provider
            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
            android:exported="false"
            android:authorities="com.sw.android.seal_project.flutter.image_provider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_image_picker_file_paths"/>
        </provider>
        <service
            android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES"/>
            </intent-filter>
            <meta-data
                android:name="photopicker_activity:0:required"
                android:value=""/>
        </service>
        <activity
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
            android:exported="false"/>
        <service
            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
            android:exported="false"
            android:directBootAware="true">
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
        </service>
        <provider
            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
            android:exported="false"
            android:authorities="com.sw.android.seal_project.mlkitinitprovider"
            android:initOrder="99"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"/>
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version"/>
        <uses-library
            android:name="androidx.window.extensions"
            android:required="false"/>
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false"/>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:exported="false"
            android:authorities="com.sw.android.seal_project.androidx-startup">
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup"/>
        </provider>
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION"/>
            </intent-filter>
        </receiver>
        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false">
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct"/>
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="false"/>
        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false"/>
        <meta-data
            android:name="com.android.vending.splits.required"
            android:value="true"/>
        <meta-data
            android:name="com.android.stamp.source"
            android:value="https://play.google.com/store"/>
        <meta-data
            android:name="com.android.stamp.type"
            android:value="STAMP_TYPE_DISTRIBUTION_APK"/>
        <meta-data
            android:name="com.android.vending.splits"
            android:resource="@xml/splits0"/>
        <meta-data
            android:name="com.android.vending.derived.apk.id"
            android:value="3"/>
    </application>
</manifest>
